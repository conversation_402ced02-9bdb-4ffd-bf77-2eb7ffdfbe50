import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/utils/global_vars.dart';

/// 内置网易云音乐解析服务
class NeteaseMusicService {
  static const String _baseUrl = 'https://api.kxzjoker.cn/api/163_music';
  
  /// 根据网易云音乐分享链接获取播放信息
  static Future<PlayInfo?> getMusicPlayInfo(String shareUrl, {String quality = 'jymaster'}) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'url': shareUrl,
          'level': quality,
          'type': 'json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 200) {
          return _parsePlayInfo(data);
        } else {
          globalTalker.error('[NeteaseMusicService] API返回错误状态: ${data['status']}');
          return null;
        }
      } else {
        globalTalker.error('[NeteaseMusicService] HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      globalTalker.error('[NeteaseMusicService] 获取播放信息失败: $e');
      return null;
    }
  }

  /// 根据音乐ID获取播放信息
  static Future<PlayInfo?> getMusicPlayInfoById(String musicId, {String quality = 'jymaster'}) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'ids': musicId,
          'level': quality,
          'type': 'json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 200) {
          return _parsePlayInfo(data);
        } else {
          globalTalker.error('[NeteaseMusicService] API返回错误状态: ${data['status']}');
          return null;
        }
      } else {
        globalTalker.error('[NeteaseMusicService] HTTP请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      globalTalker.error('[NeteaseMusicService] 获取播放信息失败: $e');
      return null;
    }
  }

  /// 解析API返回的数据为PlayInfo对象
  static PlayInfo _parsePlayInfo(Map<String, dynamic> data) {
    // 解析音质信息
    final quality = Quality(
      short: _getQualityShort(data['level']),
      level: data['level'],
      bitrate: _parseBitrate(data['level']),
      format: _parseFormat(data['url']),
      size: data['size'],
    );

    return PlayInfo(
      uri: data['url'],
      quality: quality,
    );
  }

  /// 根据音质等级解析比特率
  static int _parseBitrate(String? level) {
    switch (level) {
      case 'standard':
        return 128;
      case 'exhigh':
        return 320;
      case 'lossless':
        return 999;
      case 'hires':
        return 1411;
      case 'jyeffect':
        return 320;
      case 'sky':
        return 999;
      case 'jymaster':
        return 1411;
      default:
        return 320;
    }
  }

  /// 根据URL解析音频格式
  static String? _parseFormat(String? url) {
    if (url == null) return null;
    
    if (url.contains('.mp3')) return 'mp3';
    if (url.contains('.flac')) return 'flac';
    if (url.contains('.m4a')) return 'm4a';
    if (url.contains('.wav')) return 'wav';
    
    return 'mp3'; // 默认格式
  }

  /// 获取音质简称
  static String _getQualityShort(String? level) {
    switch (level) {
      case 'standard':
        return '标准';
      case 'exhigh':
        return '极高';
      case 'lossless':
        return '无损';
      case 'hires':
        return 'Hi-Res';
      case 'jyeffect':
        return '环绕';
      case 'sky':
        return '沉浸';
      case 'jymaster':
        return '母带';
      default:
        return '未知';
    }
  }

  /// 根据当前网络状况和用户设置选择合适的音质
  static String getOptimalQuality() {
    // 这里可以根据网络状况和用户设置来选择音质
    // 暂时返回默认的超清母带音质
    return 'jymaster';
  }
}
