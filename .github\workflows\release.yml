name: "Release"
on:
  workflow_dispatch:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"
jobs:
  get_version:
    runs-on: ubuntu-latest
    outputs:
      app_version: ${{ steps.fetch_version.outputs.app_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Fetch Version
        id: fetch_version
        run: |
          sudo snap install yq
          app_version=$(yq eval '.version' pubspec.yaml)
          echo "::set-output name=app_version::$app_version"

          
  build_on_macos:
    needs: get_version
    runs-on: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'

      - name: Replace
        env:
          SOME_SECRET_KEY: ${{ secrets.SOME_SECRET_KEY }}
          SOME_REPLACE_KEY: ${{ secrets.SOME_REPLACE_KEY }}
          SOME_REPLACE_FILE: ${{ secrets.SOME_REPLACE_FILE }}
        run: |
          python tools/replace.py
      - name: Install Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "beta"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 21
  
      - name: Install Dependencies
        run: |-
          flutter pub get
          npm install -g appdmg

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Flutter build ios
        run: |-
          flutter build ios --release --no-codesign --verbose

          chmod 777 tools/unbitcode.sh
          sh tools/unbitcode.sh

          mkdir -p Payload
          mv ./build/ios/iphoneos/Runner.app Payload
          zip -r -y Payload.zip Payload/Runner.app
          mv Payload.zip AppRhyme-release-ios-unknown-${{ needs.get_version.outputs.app_version }}.ipa

      - name: Release Ios
        uses: softprops/action-gh-release@v1
        with:
          files: AppRhyme-release-ios-unknown-${{ needs.get_version.outputs.app_version }}.ipa
          prerelease: false
          tag_name: ${{ github.event.inputs.tag_name }}
          token: ${{ secrets.RELEASE_TOKEN }}

      - name: Pack DMG Installer
        run: |-
          dart pub global activate flutter_distributor
          flutter_distributor package --platform macos --target dmg

      - name: Rename
        run: |
          mv ./dist/${{ needs.get_version.outputs.app_version }}/*.dmg ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-macos-unknown-${{ needs.get_version.outputs.app_version }}.dmg
  
      - name: Release Macos
        uses: softprops/action-gh-release@v1
        with:
          files: ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-macos-unknown-${{ needs.get_version.outputs.app_version }}.dmg
          prerelease: false
          tag_name: ${{ github.event.inputs.tag_name }}
          token: ${{ secrets.RELEASE_TOKEN }}
        
  build_on_linux:
    needs: get_version
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'

      - name: Replace
        env:
          SOME_SECRET_KEY: ${{ secrets.SOME_SECRET_KEY }}
          SOME_REPLACE_KEY: ${{ secrets.SOME_REPLACE_KEY }}
          SOME_REPLACE_FILE: ${{ secrets.SOME_REPLACE_FILE }}
        run: |
          python tools/replace.py
      - uses: subosito/flutter-action@v2
        with:
          channel: "beta"
      
      - name: Install Flutter Dependency
        run: flutter pub get

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
      

      - name: Install Linux dependency
        run: |
          sudo apt-get update -y
          sudo apt install libwebkit2gtk-4.1-dev
          sudo apt-get install -y ninja-build libgtk-3-dev libfuse2
          sudo apt-get install libasound2-dev
      
      - name: Setup AppImageTool
        run: |-
          sudo mv tools/appimagetool-x86_64.AppImage /usr/local/bin/appimagetool
          sudo chmod a+x /usr/local/bin/appimagetool
      
      - name: Pack Installers
        run: |-
          dart pub global activate flutter_distributor
          flutter_distributor package --platform linux --targets appimage,deb,rpm

      - name: Rename
        run: |
          mv ./dist/${{ needs.get_version.outputs.app_version }}/*.deb ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.deb
          mv ./dist/${{ needs.get_version.outputs.app_version }}/*.rpm ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.rpm
          mv ./dist/${{ needs.get_version.outputs.app_version }}/*.AppImage ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.AppImage

      - name: Release Linux deb
        uses: softprops/action-gh-release@v1
        with:
          files: >
            ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.AppImage,
            ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.deb,
            ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-linux-x86_64-${{ needs.get_version.outputs.app_version }}.rpm,
          prerelease: false
          tag_name: ${{ github.event.inputs.tag_name }}
          token: ${{ secrets.RELEASE_TOKEN }}

  build_on_windows:
    needs: get_version
    runs-on: windows-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: true
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.8'

      - name: Replace
        env:
          SOME_SECRET_KEY: ${{ secrets.SOME_SECRET_KEY }}
          SOME_REPLACE_KEY: ${{ secrets.SOME_REPLACE_KEY }}
          SOME_REPLACE_FILE: ${{ secrets.SOME_REPLACE_FILE }}
        run: |
          python tools/replace.py

      - name: Set up signing files
        run: |
          $env:APK_SIGN_PWD="${{ secrets.APK_SIGN_PWD }}"
          $env:APK_SIGN_ALIAS="${{ secrets.APK_SIGN_ALIAS }}"
          $env:APK_SIGN_JKS="${{ secrets.APK_SIGN_JKS }}"
          python tools/create_signing_files.py
        shell: pwsh

      - name: Print contents and length
        run: |
          Get-Content android\key.properties
          $keyPropertiesLength = (Get-Content android\key.properties | Out-String).Length
          echo "key.properties length: $keyPropertiesLength"
          $keyJksLength = (Get-Item android\app\key.jks).length
          echo "key.jks length: $keyJksLength"
        shell: pwsh

      - uses: subosito/flutter-action@v2
        with:
          channel: "stable"

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3

      - name: Install Flutter Dependency
        run: flutter pub get

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install Cargo-Ndk
        run: cargo install cargo-ndk --version 2.11.0 --force

      - name: Install android ndk
        uses: nttld/setup-ndk@v1
        id: install-ndk
        with:
          ndk-version: r22b

      - name: Activate Dependencies
        shell: pwsh
        run: dart pub global activate flutter_distributor

      - name: Setup Inno Setup 6
        shell: pwsh
        run: |-
          ./tools/is.exe /VERYSLIENT
          Copy-Item "./tools/ChineseSimplified.isl" -Destination "C:\Program Files (x86)\Inno Setup 6\Languages"

      - name: Pack EXE Installer
        run: flutter_distributor package --platform windows --target exe

      - name: Rename
        run: |
          mv ./dist/${{ needs.get_version.outputs.app_version }}/*.exe ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-windows-x86_64-${{ needs.get_version.outputs.app_version }}-setup.exe
     
      - name: Release  Windows
        uses: softprops/action-gh-release@v1
        with:
          files: ./dist/${{ needs.get_version.outputs.app_version }}/AppRhyme-release-windows-x86_64-${{ needs.get_version.outputs.app_version }}-setup.exe
          prerelease: false
          tag_name: ${{ github.event.inputs.tag_name }}
          token: ${{ secrets.RELEASE_TOKEN }}

      - name: Flutter build apk abi
        run: flutter build apk --release --split-per-abi --verbose
      
      - name: Flutter build apk
        run: flutter build apk --release --verbose      

      - name: Rename APKs
        run: |
          mv build/app/outputs/flutter-apk/app-arm64-v8a-release.apk build/app/outputs/flutter-apk/AppRhyme-release-android-arm64_v8a-${{needs.get_version.outputs.app_version}}.apk
          mv build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk build/app/outputs/flutter-apk/AppRhyme-release-android-armeabi_v7a-${{needs.get_version.outputs.app_version}}.apk
          mv build/app/outputs/flutter-apk/app-x86_64-release.apk build/app/outputs/flutter-apk/AppRhyme-release-android-x86_64-${{needs.get_version.outputs.app_version}}.apk
          mv build/app/outputs/flutter-apk/app-release.apk build/app/outputs/flutter-apk/AppRhyme-release-android-unknown-${{needs.get_version.outputs.app_version}}.apk
  
      - name: Release Apks
        uses: softprops/action-gh-release@v1
        with:
          files: >
            build/app/outputs/flutter-apk/AppRhyme-release-android-arm64_v8a-${{needs.get_version.outputs.app_version}}.apk,
            build/app/outputs/flutter-apk/AppRhyme-release-android-armeabi_v7a-${{needs.get_version.outputs.app_version}}.apk,
            build/app/outputs/flutter-apk/AppRhyme-release-android-x86_64-${{needs.get_version.outputs.app_version}}.apk,
            build/app/outputs/flutter-apk/AppRhyme-release-android-unknown-${{needs.get_version.outputs.app_version}}.apk,
          prerelease: false
          tag_name: ${{ github.event.inputs.tag_name }}
          token: ${{ secrets.RELEASE_TOKEN }}
