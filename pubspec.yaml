name: app_rhyme
description: "A Powerfull CrossPlatform Music App"
publish_to: "none"

version: 1.0.9

environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  rust_lib_app_rhyme:
    path: rust_builder

  cupertino_icons: ^1.0.6
  flutter_rust_bridge: 2.0.0
  interactive_slider:
    git: https://github.com/canxin121/interactive_slider
  dismissible_page: ^1.0.2
  get: ^4.6.6
  audio_service: ^0.18.13
  infinite_scroll_pagination: ^4.0.0
  path_provider: ^2.1.3
  image_picker: ^1.1.0
  chinese_font_library: ^1.1.0
  dart_eval: ^0.7.9
  file_picker: ^8.0.3
  flutter_lyric:
    git: https://github.com/canxin121/flutter_lyric
  talker: ^4.1.5
  just_audio_background: ^0.0.1-beta.11
  audio_session: ^0.1.19
  talker_flutter: ^4.1.5
  pull_down_button: ^0.9.4
  glassmorphism_ui: ^0.3.0
  toastification: ^2.0.0
  synchronized: ^3.1.0+1
  just_audio_media_kit: ^2.0.0
  media_kit_libs_linux: any
  # media_kit_libs_windows_audio: any
  just_audio: ^0.9.38
  just_audio_windows:
    git:
      url: https://github.com/bdlukaa/just_audio_windows
      path: just_audio_windows
  package_info_plus: ^8.0.0
  drag_select_grid_view: ^0.6.2

  extended_image: ^8.2.1
  url_launcher: ^6.3.0
  connectivity_plus: ^6.0.3
  permission_handler: ^11.3.1
  flutter_exit_app: ^1.1.3
  back_button_interceptor: ^7.0.3
  reorderables: ^0.6.0
  flutter_keyboard_visibility: ^6.0.0
  better_cupertino_slider: ^1.0.0
  bitsdojo_window: ^0.1.6


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  integration_test:
    sdk: flutter
  flutter_launcher_icons: "^0.13.1"
  package_rename: ^1.5.3

flutter_launcher_icons:
  image_path: "assets/log-0.1.0.png"
  android: true
  ios: true
  remove_alpha_ios: true
  windows:
    generate: true
    icon_size: 256
  macos:
    generate: true

package_rename_config:
  android:
    app_name: AppRhyme
    package_name: canxin.app.rhyme

  ios:
    app_name: AppRhyme
    bundle_name: AppRhyme
    package_name: canxin.app.rhyme

  linux:
    app_name: AppRhyme
    package_name: canxin.app.rhyme
    exe_name: AppRhyme

  macos:
    app_name: AppRhyme
    package_name: com.canxin.AppRhyme
    copyright_notice: Copyright ©️ 2024 Canxin121. All rights reserved.

  windows:
    app_name: AppRhyme
    organization: Canxin
    exe_name: AppRhyme

flutter:
  uses-material-design: true

  assets:
    - assets/
