// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

class ExtraInfo {
  final int? playCount;
  final int? musicCount;

  const ExtraInfo({
    this.playCount,
    this.musicCount,
  });

  @override
  int get hashCode => playCount.hashCode ^ musicCount.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExtraInfo &&
          runtimeType == other.runtimeType &&
          playCount == other.playCount &&
          musicCount == other.musicCount;
}

class MusicFuzzFilter {
  final String? name;
  final List<String> artist;
  final String? album;

  const MusicFuzzFilter({
    this.name,
    required this.artist,
    this.album,
  });

  @override
  int get hashCode => name.hashCode ^ artist.hashCode ^ album.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MusicFuzzFilter &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          artist == other.artist &&
          album == other.album;
}

class MusicInfo {
  final PlatformInt64 id;
  final String source;
  final String name;
  final List<String> artist;
  final int? duration;
  final String? album;
  final List<Quality> qualities;
  final Quality? defaultQuality;
  final String? artPic;
  String? lyric;

  MusicInfo({
    required this.id,
    required this.source,
    required this.name,
    required this.artist,
    this.duration,
    this.album,
    required this.qualities,
    this.defaultQuality,
    this.artPic,
    this.lyric,
  });

  @override
  int get hashCode =>
      id.hashCode ^
      source.hashCode ^
      name.hashCode ^
      artist.hashCode ^
      duration.hashCode ^
      album.hashCode ^
      qualities.hashCode ^
      defaultQuality.hashCode ^
      artPic.hashCode ^
      lyric.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MusicInfo &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          source == other.source &&
          name == other.name &&
          artist == other.artist &&
          duration == other.duration &&
          album == other.album &&
          qualities == other.qualities &&
          defaultQuality == other.defaultQuality &&
          artPic == other.artPic &&
          lyric == other.lyric;
}

class MusicListInfo {
  final PlatformInt64 id;
  final String name;
  final String artPic;
  final String desc;
  final ExtraInfo? extra;

  const MusicListInfo({
    required this.id,
    required this.name,
    required this.artPic,
    required this.desc,
    this.extra,
  });

  @override
  int get hashCode =>
      id.hashCode ^
      name.hashCode ^
      artPic.hashCode ^
      desc.hashCode ^
      extra.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MusicListInfo &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          artPic == other.artPic &&
          desc == other.desc &&
          extra == other.extra;
}

class Quality {
  final String short;
  final String? level;
  final int? bitrate;
  final String? format;
  final String? size;

  const Quality({
    required this.short,
    this.level,
    this.bitrate,
    this.format,
    this.size,
  });

  @override
  int get hashCode =>
      short.hashCode ^
      level.hashCode ^
      bitrate.hashCode ^
      format.hashCode ^
      size.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Quality &&
          runtimeType == other.runtimeType &&
          short == other.short &&
          level == other.level &&
          bitrate == other.bitrate &&
          format == other.format &&
          size == other.size;
}
