import 'dart:io';

import 'package:app_rhyme/src/rust/api/cache/music_cache.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:audio_service/audio_service.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/services/netease_music_service.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

// 这个结构代表了待播音乐的信息
class MusicContainer {
  late MusicAggregatorW aggregator;
  late MusicW currentMusic;
  late MusicInfo info;
  late String? extra;
  // 从Api or 本地获取的真实待播放的音质信息
  late Rx<Quality?> currentQuality;
  PlayInfo? playInfo;
  // 待播放的音频资源
  late AudioSource audioSource;
  // 已经使用过的音乐源，用于自动换源时选择下一个源
  List<String> usedSources = [];
  // 上次更新时间，用于判断是否需要更新
  DateTime lastUpdate = DateTime(1999);

  MusicContainer(MusicAggregatorW aggregator_) {
    aggregator = aggregator_;
    currentMusic = aggregator_.getDefaultMusic();
    info = currentMusic.getMusicInfo();
    _updateQuality();
    audioSource = AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
  }

  // 使上次更新时间过期
  setOutdate() {
    lastUpdate = DateTime(1999);
  }

  String toCacheFileName() {
    return "${info.name}_${info.artist.join(",")}_${currentQuality.value!.short}.${currentQuality.value!.format ?? "unknown"}";
  }

  // 检查音乐是否需要更新
  bool shouldUpdate() {
    try {
      return (audioSource as ProgressiveAudioSource)
              .uri
              .path
              .contains("/assets/") ||
          DateTime.now().difference(lastUpdate).abs().inSeconds >= 1800;
    } catch (_) {
      return true;
    }
  }

  // 是否有缓存
  Future<bool> hasCache() async {
    try {
      return hasCachePlayinfo(musicInfo: info);
    } catch (e) {
      return false;
    }
  }

  // 更新音乐内部的播放信息和音频资源
  // quality: 指定音质，如果不指定则使用默认音质
  // 会在 主动获取 或者 LazyLoad 时使用
  // 如果获取失败，则会尝试换源
  // 如果换源后仍失败，则会返回false
  Future<bool> updateAll([Quality? quality]) async {
    bool success = await _updateAudioSource(quality);
    if (success) {
      await _updateLyric();
    }
    return success;
  }

  Future<PlayInfo?> getCurrentMusicPlayInfo([Quality? quality_]) async {
    // 更新当前音质, 每次都更新以适配网络变化
    _updateQuality(quality_);

    late Quality finalQuality;
    if (quality_ != null) {
      finalQuality = quality_;
    } else if (currentQuality.value != null) {
      finalQuality = currentQuality.value!;
    } else {
      LogToast.error("获取播放信息失败", "未找到可用音质",
          "[getCurrentMusicPlayInfo] Failed to get play info, no quality found");
      return null;
    }
    // 更新extra信息
    extra = currentMusic.getExtraInfo(quality: finalQuality);

    // // 有本地缓存直接返回
    try {
      playInfo = await getCachePlayinfo(musicInfo: info);
      if (playInfo != null) {
        globalTalker.info("[getCurrentMusicPlayInfo] 使用缓存歌曲: ${info.name}");
        currentQuality.value = playInfo!.quality;
        return playInfo!;
      }
      // ignore: empty_catches
    } catch (e) {}

    // 没有本地缓存，使用内置网易云音乐解析服务
    try {
      // 尝试从extra信息中提取网易云音乐ID或分享链接
      String? musicId = _extractNeteaseId(extra);
      if (musicId != null) {
        // 根据音质选择合适的质量参数
        String qualityParam = _mapQualityToNetease(finalQuality);
        playInfo = await NeteaseMusicService.getMusicPlayInfoById(musicId, quality: qualityParam);
      } else {
        // 如果无法提取ID，尝试构造分享链接
        String shareUrl = _constructNeteaseShareUrl(info);
        String qualityParam = _mapQualityToNetease(finalQuality);
        playInfo = await NeteaseMusicService.getMusicPlayInfo(shareUrl, quality: qualityParam);
      }

      if (playInfo != null) {
        currentQuality.value = playInfo!.quality;
        globalTalker.info(
            "[getCurrentMusicPlayInfo] 使用内置网易云音乐解析获取playinfo: [${info.source}]${info.name}");
        return playInfo;
      } else {
        globalTalker.error(
            "[getCurrentMusicPlayInfo] 内置网易云音乐解析无法获取到playinfo: [${info.source}]${info.name}");
        return null;
      }
    } catch (e) {
      globalTalker.error(
          "[getCurrentMusicPlayInfo] 内置网易云音乐解析出错: $e");
      return null;
    }
  }

  // 将音乐信息转化为MediaItem, 用于AudioService在系统显示音频信息
  MediaItem _toMediaItem() {
    Uri? artUri;
    if (info.artPic != null) {
      artUri = Uri.parse(info.artPic!);
    } else {
      artUri = null;
    }
    return MediaItem(
        id: extra.hashCode.toString(),
        title: info.name,
        album: info.album,
        artUri: artUri,
        artist: info.artist.join(","));
  }

  Future<void> _updateLyric() async {
    if (info.lyric == null || info.lyric!.isEmpty) {
      try {
        var lyric = await aggregator.fetchLyric();
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 歌词成功");
        info.lyric = lyric;
      } catch (e) {
        LogToast.error("更新歌词失败", "在线更新歌词失败: $e",
            "[MusicContainer] Failed to update lyric: $e");
        info.lyric = "[00:00.00]获取歌词失败";
      }
    }
  }

  Future<bool> _updateAudioSource([Quality? quality]) async {
    lastUpdate = DateTime.now();
    if (quality != null) extra = currentMusic.getExtraInfo(quality: quality);
    while (true) {
      try {
        playInfo = await getCurrentMusicPlayInfo(quality);
      } catch (e) {
        playInfo = null;
      }
      if (playInfo != null) {
        // 更新当前音质
        currentQuality.value = playInfo!.quality;

        if (playInfo!.uri.contains("http")) {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.parse(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource =
                AudioSource.uri(Uri.parse(playInfo!.uri), tag: _toMediaItem());
          }
        } else {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.file(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource = AudioSource.file(playInfo!.uri, tag: _toMediaItem());
          }
        }
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 音频资源成功");
        return true;
      } else {
        LogToast.error("更新播放资源失败", "${info.name}更新播放资源失败, 尝试换源播放",
            "[MusicContainer] Failed to update audio source, try to change source");
        bool changed = await _changeSource();
        if (!changed) {
          return false;
        }
      }
    }
  }

  Future<bool> _changeSource([String? source]) async {
    // 自动换源功能已删除，直接返回false
    LogToast.error("切换音乐源失败", "${info.name}自动换源功能已禁用",
        "[MusicContainer] Auto source switching is disabled");
    return false;
  }

  void _updateQuality([Quality? quality]) {
    if (quality != null) {
      currentQuality.value = quality;
      extra = currentMusic.getExtraInfo(quality: quality);
    } else {
      if (info.qualities.isNotEmpty) {
        currentQuality = autoPickQuality(info.qualities).obs;
        extra = currentMusic.getExtraInfo(quality: currentQuality.value!);
      } else {
        currentQuality.value = null;
        extra = null;
      }
    }
  }

  /// 从extra信息中提取网易云音乐ID
  String? _extractNeteaseId(String? extra) {
    if (extra == null) return null;

    // 尝试从extra中解析音乐ID
    // 这里需要根据实际的extra格式来实现
    // 暂时返回null，让系统使用分享链接方式
    return null;
  }

  /// 构造网易云音乐分享链接
  String _constructNeteaseShareUrl(MusicInfo info) {
    // 这里需要根据音乐信息构造网易云音乐分享链接
    // 暂时使用一个示例链接格式
    // 实际实现需要根据音乐信息来构造正确的链接
    return "https://y.music.163.com/m/song?id=475479888&userid=8719916627&dlt=0846";
  }

  /// 将应用内的音质映射到网易云音乐API的音质参数
  String _mapQualityToNetease(Quality quality) {
    // 根据比特率或其他质量指标映射到网易云音乐API的质量参数
    int bitrate = quality.bitrate ?? 320; // 默认320kbps
    if (bitrate >= 1411) {
      return 'jymaster'; // 超清母带
    } else if (bitrate >= 999) {
      return 'lossless'; // 无损音质
    } else if (bitrate >= 320) {
      return 'exhigh'; // 极高品质
    } else {
      return 'standard'; // 标准音质
    }
  }
}
