import 'package:app_rhyme/src/rust/api/types/version.dart';

import 'package:app_rhyme/utils/log_toast.dart';
import 'package:app_rhyme/dialogs/extern_api_update_dialog.dart';
import 'package:app_rhyme/dialogs/version_update_dialog.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:flutter/widgets.dart';

Future<void> checkVersionUpdate(BuildContext context, bool toast) async {
  try {
    if (toast) {
      LogToast.info("检查应用版本更新", "正在加载数据,请稍等",
          "[checkVersionUpdate] Checking app version update");
    }

    var release = await checkUpdate(currentVersion: globalPackageInfo.version);
    if (context.mounted && release != null) {
      showVersionUpdateDialog(context, release);
    } else if (release == null) {
      if (toast) {
        LogToast.info("版本更新", "当前版本无需更新",
            "[checkVersionUpdate] Current version does not need to be updated");
      }
    }
  } catch (e) {
    globalTalker.log("[VersionUpdate] $e");
  }
}

Future<void> checkExternApiUpdate(BuildContext context, bool toast) async {
  // 外部API更新功能已删除，改为内置网易云音乐解析
  if (toast) {
    LogToast.info("检查更新", "已使用内置网易云音乐解析，无需更新外部源",
        "[checkExternApiUpdate] Using built-in Netease music service, no external source update needed");
  }
}

Future<void> autoCheckUpdate(BuildContext context) async {
  if (globalConfig.versionAutoUpdate) {
    await checkVersionUpdate(context, false);
  }
  // 外部API自动更新功能已删除
}
